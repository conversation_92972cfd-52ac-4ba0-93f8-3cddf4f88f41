import { 
  mixpanelCustomEvent, MixpanelEventName,
} from '@/common/utils/mixpanel/eventTriggers';
import { ButtonDistribution } from '@/common/hooks/useButtonDistribution';

export type ButtonType = 'watchlist' | 'rate_showcase';

export interface ABTestResult {
  buttonType: ButtonType;
  shouldShowRateButton: boolean;
}

/**
 * Determines which button to show based on smart 50/50 A/B testing
 * that adjusts based on current distribution to maintain balance
 */
export const determineButtonDisplay = (
  distribution: ButtonDistribution,
  slug: string,
  userId?: string
): ABTestResult => {
  try {
    // Calculate adjustment factor to maintain 50/50 balance
    let watchlistProbability = 0.5; // Default 50%
    
    if (distribution.total > 10) { // Only adjust if we have enough data
      const watchlistPercentage = distribution.watchlistPercentage;
      
      // If watchlist is shown more than 55%, reduce its probability
      // If watchlist is shown less than 45%, increase its probability
      if (watchlistPercentage > 55) {
        watchlistProbability = 0.3; // Favor rate showcase
      } else if (watchlistPercentage < 45) {
        watchlistProbability = 0.7; // Favor watchlist
      }
      // If between 45-55%, keep it at 50%
    }

    // Generate deterministic but pseudo-random decision based on user/session
    const seed = userId || getSessionSeed();
    const random = seededRandom(seed + slug);
    
    const showWatchlist = random < watchlistProbability;
    const buttonType: ButtonType = showWatchlist ? 'watchlist' : 'rate_showcase';
    
    // Track the button display
    trackButtonDisplay(buttonType, slug, distribution);
    
    return {
      buttonType,
      shouldShowRateButton: !showWatchlist,
    };
  } catch (error) {
    console.error('Error in determineButtonDisplay:', error);
    
    // Fallback to simple random 50/50
    const fallbackRandom = Math.random();
    const buttonType: ButtonType = fallbackRandom < 0.5 ? 'watchlist' : 'rate_showcase';
    
    trackButtonDisplay(buttonType, slug, distribution);
    
    return {
      buttonType,
      shouldShowRateButton: buttonType === 'rate_showcase',
    };
  }
};

/**
 * Tracks button display event in Mixpanel
 */
const trackButtonDisplay = (
  buttonType: ButtonType,
  slug: string,
  distribution: ButtonDistribution
) => {
  mixpanelCustomEvent({
    eventName: MixpanelEventName.buttonDisplayed,
    mixpanelProps: {
      page: 'Showcase',
      slug: slug,
      buttonType: buttonType,
      currentWatchlistPercentage: distribution.watchlistPercentage,
      currentRateShowcasePercentage: distribution.rateShowcasePercentage,
      totalEvents: distribution.total,
    },
  });
};

/**
 * Gets or creates a session-based seed for consistent user experience
 */
const getSessionSeed = (): string => {
  if (typeof window === 'undefined') {
    return 'server-fallback';
  }
  
  let seed = sessionStorage.getItem('ab-test-seed');
  if (!seed) {
    seed = Math.random().toString(36).substring(2, 15);
    sessionStorage.setItem('ab-test-seed', seed);
  }
  return seed;
};

/**
 * Simple seeded random number generator for consistent results
 */
const seededRandom = (seed: string): number => {
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Convert to positive number and normalize to 0-1 range
  const normalized = Math.abs(hash) / 2147483647;
  return normalized;
};

/**
 * Determines if user should see both buttons (after login)
 */
export const shouldShowBothButtons = (isAuthenticated: boolean): boolean => {
  return isAuthenticated;
};
