'use client';

import {
  useEffect,
  useRef,
  useState,
} from "react";
import SectionLayout from "@/app/company/[slug]/sectionLayout";
import { TitleSectionProps } from "@/app/company/[slug]/types";
import {
  Button,
  FlipWords,
} from "@/common/components/atoms";
import StayUpdatedPopup from "@/app/company/[slug]/sections/StayUpdatedPopup";
import {
  usePathname,
} from "next/navigation";
import Image from "next/image";
import {
  Check, X,
} from "lucide-react";
import {
  motion,
  AnimatePresence,
} from "framer-motion";
import { Modal } from "@/common/components/molecules";
import {
  mixpanelCustomEvent, MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import {
  COMPANY_SIZE_MAP,
  REGION_MAP,
} from "@/app/company/[slug]/types";
import Copilot from "./Copilot";
import Header from "./Header";
import ThreeTabLayout from "./Hero/3TabLayout";
import Link from "next/link";
import { routes } from "@/common/routes";
import {
  useOutsideClick, useScrollDirection, useWindowDimensions,
} from "@/common/hooks";
import WatchlistSidebar from "./WatchlistSidebar";
import dayjs from "dayjs";
declare global {
  interface Window {
    fbq: (event: string, action: string, data: any) => void;
  }
}
const TitleSection = ({
  title, heroCTALink, graphLink, handleAddToWatchlist, handleRateStock, rateStockLabel, showRateStockButton, updatedAt, industries, tags, price, changePercent, watchlistCount, isWatchlisted, primaryCTALabel, setIsWatchlisted, discoveryCoverImage, enableCurationAI, heroCTALabel, slug, logo, subtitle, teaserVideoLink, teaserVideoLabel, privateShowcase, companyName, teaserVideoCaption, teaserVideoTitle, exchange, symbol, buy, sell, prompts,
}: TitleSectionProps) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [copilotOpened, setCopilotOpened] = useState(true);
  const [openWatchlistInfo, setOpenWatchlistInfo] = useState(false);
  const [showWatchlistSidebar, setShowWatchlistSidebar] = useState(false);
  const [isMainButtonVisible, setIsMainButtonVisible] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [cameFromRateShowcase, setCameFromRateShowcase] = useState(false);
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const pathname = usePathname();
  const { isVisible } = useScrollDirection();
  const { windowSize } = useWindowDimensions();
  const isMobile = windowSize === 'mobile';
  const mobileScrollThreshold = 200;

  const watchlistRef = useRef<HTMLDivElement>(null);
  const mainButtonRef = useRef<HTMLDivElement>(null);
  const mobileButtonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const sessionShowcasePath = sessionStorage.getItem('ai_showcasePath')
    if (sessionShowcasePath && pathname !== sessionShowcasePath) {
      sessionStorage.removeItem('threadId')
      sessionStorage.removeItem('ai_showcasePath')
    }
  }, [pathname])

  useEffect(() => {
    const rateShowcaseFlag = localStorage.getItem('cameFromRateShowcase');
    setCameFromRateShowcase(rateShowcaseFlag === 'true');
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    if (isMobile && !mobileButtonRef.current) {
      return;
    }

    if (!isMobile && !mainButtonRef.current) {
      return;
    }

    const currentRef = isMobile ? mobileButtonRef.current : mainButtonRef.current;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsMainButtonVisible(entry.isIntersecting);
      },
      { threshold: 0.5 },
    );

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [isMobile]);

  const handleCTAClick = (source?: string) => {
    if (privateShowcase) {
      window.open(heroCTALink, "_blank");
      mixpanelCustomEvent({
        eventName: MixpanelEventName.followUsClicked,
        mixpanelProps: {
          page: 'Showcase',
          slug: slug,
          source: source || 'Hero section',
          label: heroCTALabel,
        },
      })
    } else {
      if (isWatchlisted) {
        setShowWatchlistSidebar(true)
      } else {
        handleAddToWatchlist("hero-section")
        setIsWatchlisted(true)
        window.fbq('track', 'AddToCart', {
          page: 'Showcase',
          slug: slug,
          source: source || 'Hero section',
          label: heroCTALabel,
        });
        setTimeout(() => {
          setShowWatchlistSidebar(true)
        }, 2000)
      }
    }
  }

  const handleRateStockClick = (source?: string) => {
    if (handleRateStock) {
      handleRateStock(source || 'Hero section');
    }
  }

  useOutsideClick({
    isVisible: true,
    ref: watchlistRef,
    callback: () => setOpenWatchlistInfo(false),
  })
  const handleGoToWatchlist = () => {
    mixpanelCustomEvent({
      eventName: MixpanelEventName.watchlistPopupClicked,
      mixpanelProps: {
        slug: slug,
        source: 'Hero section',
      },
    })
  }
  return (
    <SectionLayout className="z-10">
      <Header
        slug={slug}
        watchlistCount={watchlistCount}
        isAssistantAvailable={enableCurationAI}
        setCopilotOpened={setCopilotOpened}
        price={price}
        onMobileNavToggle={setIsMobileNavOpen}
      />
      <StayUpdatedPopup
        privateShowcase={privateShowcase}
        isWatchlisted={isWatchlisted}
        copilotOpened={copilotOpened}
        heroCTALabel={heroCTALabel}
        heroCTALink={heroCTALink}
        handleAddToWatchlist={handleAddToWatchlist}
        heroVideoPlaying={modalOpen}
        slug={slug}
      />
      {enableCurationAI && (
        <Copilot
          slug={slug}
          companyName={companyName || ''}
          heroCTALabel={heroCTALabel || ''}
          heroCTALink={heroCTALink || ''}
          copilotOpened={copilotOpened}
          setCopilotOpened={setCopilotOpened}
          prompts={prompts}
        />
      )}

      <WatchlistSidebar
        isOpen={showWatchlistSidebar}
        onClose={() => setShowWatchlistSidebar(false)}
        source="hero-section"
      />

      <div className={`flex flex-col gap-4 pt-8 sm:pt-12`}>
        <AnimatePresence>
          {isVisible && !isMainButtonVisible && (!isWatchlisted || cameFromRateShowcase) && (!isMobile || (isMobile && scrollPosition > mobileScrollThreshold)) && !isMobileNavOpen && (
            <motion.div
              initial={{
                opacity: 0,
                y: -20,
              }}
              animate={{
                opacity: 1,
                y: 0,
              }}
              exit={{
                opacity: 0,
                y: -20,
              }}
              transition={{ duration: 0.3 }}
              className="fixed top-[66px] xl:top-[73px] w-full left-0 right-0 z-20 px-4 sm:px-8 lg:px-16 xl:px-32 bg-[#0E0E0F90] backdrop-blur-sm"
            >
              <div className="max-w-[1200px] mx-auto flex justify-between items-center py-2">
                <Image
                  src={logo}
                  alt="logo"
                  width={116}
                  height={50}
                  className="rounded-sm max-h-14 w-auto"
                />
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="primary"
                    size="lg"
                    className={`capitalize sm:text-base text-sm !py-1.5 sm:!py-2.5 !px-4 sm:!px-8 whitespace-nowrap font-medium ${isWatchlisted ? "!bg-white enabled:hover:!bg-white/90 !text-[#3F3D42]" : ""} !ring-0 focus:!ring-0`}
                    onClick={() => handleCTAClick('Hero Section Sticky Button')}>
                    {primaryCTALabel()}
                    {isWatchlisted && !privateShowcase && !cameFromRateShowcase ? (
                      <span className="bg-[#3F3D42] rounded-full w-[18px] h-[18px] flex justify-center items-center ml-2 text-white">
                        <Check width={12} height={12} />
                      </span>
                    ) : null}
                  </Button>
                  {showRateStockButton && rateStockLabel && (
                    <Button
                      type="button"
                      variant="secondary"
                      size="lg"
                      className="capitalize sm:text-base text-sm !py-1.5 sm:!py-2.5 !px-4 sm:!px-8 whitespace-nowrap !font-medium !ring-0 focus:!ring-0 !bg-[#272629] !text-white hover:!bg-[#3F3D42]"
                      onClick={() => handleRateStockClick('Hero Section Sticky Button')}>
                      {rateStockLabel()}
                    </Button>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.div
          initial={{
            opacity: 0,
            y: 10,
            filter: "blur(2px)",
          }}
          animate={{
            opacity: 1,
            y: 0,
            filter: "blur(0px)",
          }}
          transition={{
            type: "spring",
            delay: 0.5,
          }}
          className="flex justify-between items-center"
        >
          <Image
            src={logo}
            alt="logo"
            width={116}
            height={50}
            className="rounded-sm max-h-14 w-auto"

          />
          <div ref={mainButtonRef} className="flex gap-3">
            <Button
              type="button"
              variant="primary"
              size="lg"
              className={`capitalize hidden sm:flex !py-2.5 px-8 whitespace-nowrap font-medium ${isWatchlisted ? "!bg-white enabled:hover:!bg-white/90 !text-[#3F3D42]" : ""} !ring-0 focus:!ring-0`}
              onClick={() => handleCTAClick()}>
              {primaryCTALabel()}
              {isWatchlisted && !privateShowcase && !cameFromRateShowcase ? (
                <span className="bg-[#3F3D42] rounded-full w-[18px] h-[18px] flex justify-center items-center ml-2 text-white">
                  <Check width={12} height={12} />
                </span>
              ) : null}
            </Button>
            {showRateStockButton && rateStockLabel && (
              <Button
                type="button"
                variant="secondary"
                size="lg"
                className="capitalize hidden sm:flex !py-2.5 px-8 whitespace-nowrap !font-medium !ring-0 focus:!ring-0 !bg-[#272629] !text-white hover:!bg-[#3F3D42]"
                onClick={() => handleRateStockClick()}>
                {rateStockLabel()}
              </Button>
            )}
          </div>
        </motion.div>
        <motion.p
          initial={{
            opacity: 0,
            y: 10,
            filter: "blur(2px)",
          }}
          animate={{
            opacity: 1,
            y: 0,
            filter: "blur(0px)",
          }}
          transition={{
            type: "spring",
            delay: 1,
          }}
          className="mt-2 text-white font-semibold text-4xl sm:text-5xl"
        >
          {companyName}
        </motion.p>
        <FlipWords title={title} className="text-2xl font-semibold max-w-[640px] text-white" />
        {subtitle ? (
          <motion.p
            initial={{
              opacity: 0,
              y: 10,
              filter: "blur(2px)",
            }}
            animate={{
              opacity: 1,
              y: 0,
              filter: "blur(0px)",
            }}
            transition={{
              type: "spring",
              delay: 1.3,
            }}
            className="text-[#D4D4D4] max-w-[640px]"
          >
            {subtitle}
          </motion.p>
        ) : null}
        <motion.div
          initial={{
            opacity: 0,
            y: 10,
          }}
          animate={{
            opacity: 1,
            y: 0,
          }}
          transition={{
            type: "spring",
            delay: 1.4,
          }}
          className="flex flex-wrap gap-2 max-w-[600px]"
        >
          {industries?.map((industry) => (
            <span key={industry.id} className="bg-[#272629] font-medium text-white text-xs px-2 py-[5px] rounded-md">
              {industry.name}
            </span>
          ))}
          {tags?.region && (
            <span className="bg-[#272629] font-medium text-white text-xs px-2 py-[5px] rounded-md">
              {REGION_MAP[tags.region]}
            </span>
          )}
          {exchange && (
            <span className="bg-[#272629] font-medium text-white text-xs px-2 py-[5px] rounded-md">
              {exchange}
            </span>
          )}
          {tags?.company_size && (
            <span className="bg-[#272629] font-medium text-white text-xs px-2 py-[5px] rounded-md">
              {COMPANY_SIZE_MAP[tags.company_size]}
            </span>
          )}
        </motion.div>
        <motion.div
          initial={{
            opacity: 0,
            y: 10,
          }}
          animate={{
            opacity: 1,
            y: 0,
          }}
          transition={{
            type: "spring",
            delay: 1.4,
          }}
          className="text-sm text-[#BAB9BD]"
        >
          <div>Author: Curation</div>
          {updatedAt ? (<div>Showcase last updated: {updatedAt ? dayjs(updatedAt).format('MMM, YYYY') : ''}</div>) : null}
        </motion.div>
        <div className="">
          <motion.div
            initial={{
              opacity: 0,
              y: 10,
            }}
            animate={{
              opacity: 1,
              y: 0,
            }}
            transition={{
              type: "spring",
              delay: 1.5,
            }}
            className="relative"
            ref={watchlistRef}
          >
            <div ref={mobileButtonRef} className="flex flex-col gap-2 w-full">
              <Button
                type="button"
                variant="primary"
                size="lg"
                className={`sm:hidden mt-2 capitalize !py-2.5 px-8 whitespace-nowrap font-medium ${isWatchlisted ? "!bg-white enabled:hover:!bg-white/90 !text-[#3F3D42]" : ""} !ring-0 focus:!ring-0`}
                onClick={() => handleCTAClick()}>
                {primaryCTALabel()}
                {isWatchlisted && !privateShowcase && !cameFromRateShowcase ? (
                  <span className="bg-[#3F3D42] rounded-full w-[18px] h-[18px] flex justify-center items-center ml-2 text-white">
                    <Check width={12} height={12} />
                  </span>
                ) : null}
              </Button>
              {showRateStockButton && rateStockLabel && (
                <Button
                  type="button"
                  variant="secondary"
                  size="lg"
                  className="sm:hidden capitalize !py-2.5 px-8 whitespace-nowrap !font-medium !ring-0 focus:!ring-0 !bg-[#272629] !text-white hover:!bg-[#3F3D42]"
                  onClick={() => handleRateStockClick()}>
                  {rateStockLabel()}
                </Button>
              )}
            </div>
            {openWatchlistInfo && (
              <div className="absolute top-[calc(100%+2px)] left-0 w-[240px] z-50 bg-[#3F3D42] rounded-lg p-4 text-white">
                <button onClick={() => setOpenWatchlistInfo(false)} className="absolute top-2 right-2" type="button"><X width={16} height={16} /></button>
                <div className=" text-[#D9D7DB] font-semibold">Following Showcase</div>
                <div className="text-sm text-[#D9D7DB]">You'll receive price alerts and key news in your newsletter</div>
                <Link
                  href={routes.watchlist}
                  onClick={handleGoToWatchlist}
                  className="w-auto flex mt-3 items-center justify-center bg-white text-[#000] shadow-sm rounded-lg
                        disabled:bg-white/40
                        disabled:text-button-neutral-600 disabled:cursor-not-allowed dark:border-button-secondary-border-dark dark:bg-button-secondary-bg-dark dark:text-button-secondary-text-dark
                        dark:hover:enabled:bg-button-secondary-bg-hover-dark dark:hover:text-button-secondary-text-hover-dark
                        outline-none px-4 text-sm font-medium !border-0 py-2"
                >
                  Go to Watchlist
                </Link>
              </div>
            )}
          </motion.div>
        </div>
      </div>
      <ThreeTabLayout
        videoSrc={teaserVideoLink}
        videoLabel={teaserVideoLabel}
        teaserVideoCaption={teaserVideoCaption}
        discoveryCoverImage={discoveryCoverImage}
        teaserVideoTitle={teaserVideoTitle}
        exchange={exchange}
        graphLink={graphLink}
        price={price}
        changePercent={changePercent}
        symbol={symbol}
        buy={buy}
        sell={sell}
        slug={slug}
      />
      <Modal isOpen={modalOpen} onClose={() => setModalOpen(false)} maxWidth={"sm:max-w-[700px]"}>
        <iframe
          title="teaser video"
          src={teaserVideoLink}
          className="w-full h-[200px] md:h-[400px]"
        ></iframe>
      </Modal>
    </SectionLayout>
  );
}

export default TitleSection;
